@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent FOUC (Flash of Unstyled Content) and ensure proper responsive behavior */
@layer base {
  /* Use higher specificity instead of !important to override external scripts */
  html.plt-desktop,
  html.plt-mobile,
  html.plt-tablet,
  html[mode="md"],
  html[mode="ios"],
  html[mode="android"] {
    /* Reset external framework styles with specificity */
    display: block;
    width: 100%;
    height: auto;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    overflow-x: hidden;
    /* Ensure proper viewport behavior on all devices */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    width: 100%;
    height: auto;
  }

  html:not(.loaded) {
    visibility: hidden;
    opacity: 0;
  }

  html.loaded {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.15s ease-in-out;
  }

  /* Force proper responsive behavior */
  * {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
  }

  body {
    overflow-x: hidden;
    scrollbar-gutter: stable;
  }

  * {
    box-sizing: border-box;
  }

  /* Ensure containers don't exceed viewport width */
  .max-w-7xl {
    max-width: min(80rem, 100vw);
  }
}

/* Ensure Tailwind responsive classes work with higher specificity */
@layer utilities {
  /* Use CSS containment to isolate our layout from external interference */
  body {
    contain: layout style;
  }

  /* Higher specificity selectors for responsive grid classes */
  html body .grid.md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  html body .grid.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  html body .grid.md\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  @media (min-width: 768px) {
    html body .grid.md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    html body .grid.md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    html body .grid.md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 1024px) {
    html body .grid.lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    html body .grid.lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    html body .grid.lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  /* Higher specificity for flex responsive classes */
  @media (min-width: 768px) {
    html body .md\:flex-row {
      flex-direction: row;
    }

    html body .md\:flex-col {
      flex-direction: column;
    }

    html body .md\:block {
      display: block;
    }

    html body .md\:hidden {
      display: none;
    }
  }

  @media (min-width: 1024px) {
    html body .lg\:flex-row {
      flex-direction: row;
    }

    html body .lg\:flex-col {
      flex-direction: column;
    }

    html body .lg\:block {
      display: block;
    }

    html body .lg\:hidden {
      display: none;
    }
  }
}

/* Clean iframe integration for HighLevel forms */
@layer utilities {
  /* Remove any default iframe styling that might interfere */
  iframe[src*="leadconnectorhq.com"] {
    display: block !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
  }

  /* Ensure booking and form iframes display cleanly */
  iframe[src*="widget/form"],
  iframe[src*="widget/booking"] {
    border: none !important;
    outline: none !important;
    background: white !important;
  }

  /* Mobile optimization for forms */
  @media (max-width: 640px) {
    iframe[src*="widget/form"] {
      min-height: 500px;
    }

    iframe[src*="widget/booking"] {
      min-height: 600px;
    }
  }

  /* Tablet optimization */
  @media (min-width: 641px) and (max-width: 1024px) {
    iframe[src*="widget/form"] {
      height: 580px !important;
    }

    iframe[src*="widget/booking"] {
      height: 700px !important;
    }
  }
}

/* Custom Form Styling to match HighLevel design */
@layer components {
  .custom-form-input {
    @apply w-full h-9 px-4 py-2 border-2 border-gray-300 rounded-md text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors duration-200;
  }

  .custom-form-button {
    @apply w-full h-14 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold text-base rounded-md transition-colors duration-200 transform hover:scale-[1.02] active:scale-[0.98];
  }

  .custom-form-upload {
    @apply relative flex flex-col items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-md cursor-pointer transition-colors duration-200 hover:border-gray-400;
  }
}

/* Google Places Autocomplete Styling - Using CSS custom properties to avoid CORS issues */
:root {
  --pac-z-index: 9999;
  --pac-border-radius: 8px;
  --pac-border-color: #d1d5db;
  --pac-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --pac-margin-top: 2px;
  --pac-item-padding: 12px 16px;
  --pac-item-border: 1px solid #f3f4f6;
  --pac-item-font-size: 14px;
  --pac-hover-bg: #f9fafb;
  --pac-selected-bg: #eff6ff;
  --pac-matched-color: #1d4ed8;
}

/* Apply styles without using !important to avoid CORS conflicts */
.pac-container {
  z-index: var(--pac-z-index);
  border-radius: var(--pac-border-radius);
  border: 1px solid var(--pac-border-color);
  box-shadow: var(--pac-shadow);
  margin-top: var(--pac-margin-top);
  font-family: 'Inter', sans-serif;
}

.pac-item {
  padding: var(--pac-item-padding);
  border-bottom: var(--pac-item-border);
  cursor: pointer;
  font-size: var(--pac-item-font-size);
}

.pac-item:hover {
  background-color: var(--pac-hover-bg);
}

.pac-item-selected {
  background-color: var(--pac-selected-bg);
}

.pac-matched {
  font-weight: 600;
  color: var(--pac-matched-color);
}

/* Hide the Google Places dropdown when not needed */
.pac-container.pac-hidden {
  display: none !important;
}

.custom-form-checkbox {
  @apply w-4 h-4 mt-0.5 text-blue-600 bg-blue-50 border-gray-300 rounded focus:ring-blue-500 focus:ring-2;
}

/* Smooth loading animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Form field focus states */
.form-field:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Button hover effects */
.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Hide scrollbar but keep scroll functionality */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* For Firefox */
html {
  scrollbar-width: none;
}

/* Ensure proper text rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Loading state for forms */
.form-loading {
  position: relative;
}

.form-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .custom-form-input {
    border-width: 3px;
  }

  .custom-form-button {
    border: 2px solid transparent;
  }

  .custom-form-button:focus {
    border-color: white;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}


