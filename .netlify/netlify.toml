headersOrigin = "config"
redirectsOrigin = "config"
plugins = []

[functions]

[functions."*"]

[build]
publish = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/StokeLeads-2/Code/horizon/dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"

[build.environment]

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[headers]]
for = "/*.html"

[headers.values]
Cache-Control = "no-cache, no-store, must-revalidate"
Pragma = "no-cache"
Expires = "0"

[[headers]]
for = "/*.css"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.js"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
X-XSS-Protection = "1; mode=block"
Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
X-XSS-Protection = "1; mode=block"
Referrer-Policy = "strict-origin-when-cross-origin"
Cache-Control = "no-cache, no-store, must-revalidate"
Pragma = "no-cache"
Expires = "0"

[[headers]]
for = "/assets/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.css"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"
Content-Type = "text/css; charset=utf-8"

[[headers]]
for = "*.js"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"
Content-Type = "application/javascript; charset=utf-8"

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]